from typing import List, Optional

import aiosqlite
from fastapi import APIRouter, Depends, HTTPException

from ..database import get_db
from ..models import ActivityWithDetails, PlayerActivity, PlayerActivityCreate

router = APIRouter()


@router.post('/', response_model=PlayerActivity)
async def create_activity(activity: PlayerActivityCreate, db: aiosqlite.Connection = Depends(get_db)):
    """Create a new player activity"""
    # Verify airport exists
    cursor = await db.execute('SELECT id FROM airports WHERE id = ?', (activity.airport_id,))
    if not await cursor.fetchone():
        raise HTTPException(status_code=400, detail='Airport not found')

    # Insert activity
    cursor = await db.execute(
        """
        INSERT INTO player_activities (player_id, airport_id, accepted_level)
        VALUES (?, ?, ?)
    """,
        (activity.player_id, activity.airport_id, activity.accepted_level),
    )

    await db.commit()

    # Return the created activity
    activity_id = cursor.lastrowid
    db.row_factory = aiosqlite.Row
    cursor = await db.execute('SELECT * FROM player_activities WHERE id = ?', (activity_id,))
    row = await cursor.fetchone()

    return dict(row)


@router.get('/', response_model=List[ActivityWithDetails])
async def get_activities(
    skip: int = 0,
    limit: int = 100,
    player_id: Optional[str] = None,
    accepted_level: Optional[str] = None,
    db: aiosqlite.Connection = Depends(get_db),
):
    """Get player activities with optional filtering"""
    db.row_factory = aiosqlite.Row

    query = """
        SELECT
            pa.id, pa.last_active, pa.player_id, pa.accepted_level,
            a.id as airport_id, a.code, a.name, a.region
        FROM player_activities pa
        JOIN airports a ON pa.airport_id = a.id
    """

    conditions = []
    params = []

    if player_id:
        conditions.append('pa.player_id = ?')
        params.append(player_id)

    if accepted_level:
        conditions.append('pa.accepted_level = ?')
        params.append(accepted_level)

    if conditions:
        query += ' WHERE ' + ' AND '.join(conditions)

    query += ' ORDER BY pa.last_active DESC LIMIT ? OFFSET ?'
    params.extend([limit, skip])

    cursor = await db.execute(query, params)
    rows = await cursor.fetchall()

    result = []
    for row in rows:
        result.append({
            'id': row['id'],
            'last_active': row['last_active'],
            'player_id': row['player_id'],
            'accepted_level': row['accepted_level'],
            'airport': {'id': row['airport_id'], 'code': row['code'], 'name': row['name'], 'region': row['region']},
        })

    return result


@router.get('/players')
async def get_active_players(db: aiosqlite.Connection = Depends(get_db)):
    """Get list of active players"""
    db.row_factory = aiosqlite.Row
    cursor = await db.execute("""
        SELECT
            player_id,
            COUNT(*) as activity_count,
            MAX(last_active) as last_seen
        FROM player_activities
        GROUP BY player_id
        ORDER BY last_seen DESC
    """)
    rows = await cursor.fetchall()
    return [dict(row) for row in rows]


@router.get('/levels')
async def get_accepted_levels(db: aiosqlite.Connection = Depends(get_db)):
    """Get list of accepted levels"""
    db.row_factory = aiosqlite.Row
    cursor = await db.execute("""
        SELECT
            accepted_level,
            COUNT(*) as count
        FROM player_activities
        GROUP BY accepted_level
        ORDER BY accepted_level
    """)
    rows = await cursor.fetchall()
    return [dict(row) for row in rows]


@router.get('/{activity_id}', response_model=PlayerActivity)
async def get_activity(activity_id: int, db: aiosqlite.Connection = Depends(get_db)):
    """Get activity by ID"""
    db.row_factory = aiosqlite.Row
    cursor = await db.execute('SELECT * FROM player_activities WHERE id = ?', (activity_id,))
    row = await cursor.fetchone()

    if not row:
        raise HTTPException(status_code=404, detail='Activity not found')

    return dict(row)


@router.delete('/{activity_id}')
async def delete_activity(activity_id: int, db: aiosqlite.Connection = Depends(get_db)):
    """Delete an activity"""
    cursor = await db.execute('DELETE FROM player_activities WHERE id = ?', (activity_id,))

    if cursor.rowcount == 0:
        raise HTTPException(status_code=404, detail='Activity not found')

    await db.commit()
    return {'message': 'Activity deleted successfully'}
