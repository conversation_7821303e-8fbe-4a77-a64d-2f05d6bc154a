import os
from pathlib import Path
from typing import Any, Dict, List

import aiosqlite
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

DATABASE_PATH = os.getenv('DATABASE_PATH', 'data/game_tracker.db')


async def get_db():
    """Get database connection"""
    # Ensure data directory exists
    Path(DATABASE_PATH).parent.mkdir(parents=True, exist_ok=True)

    async with aiosqlite.connect(DATABASE_PATH) as db:
        # Enable foreign keys
        await db.execute('PRAGMA foreign_keys = ON')
        yield db


async def init_database():
    """Initialize database with schema and seed data"""
    Path(DATABASE_PATH).parent.mkdir(parents=True, exist_ok=True)

    async with aiosqlite.connect(DATABASE_PATH) as db:
        # Enable foreign keys
        await db.execute('PRAGMA foreign_keys = ON')

        # Create tables
        await db.execute("""
            CREATE TABLE IF NOT EXISTS airports (
                id INTEGER PRIMARY KEY,
                code VARCHAR(10) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                region VARCHAR(50)
            )
        """)

        await db.execute("""
            CREATE TABLE IF NOT EXISTS plane_models (
                id INTEGER PRIMARY KEY,
                model_name VARCHAR(50) UNIQUE NOT NULL,
                level CHAR(1) CHECK (level IN ('A','B','C','D','E','F','G')),
                type VARCHAR(10) CHECK (type IN ('PAX','CARGO'))
            )
        """)

        await db.execute("""
            CREATE TABLE IF NOT EXISTS contracts (
                id INTEGER PRIMARY KEY,
                plane_model_id INTEGER REFERENCES plane_models(id),
                from_player VARCHAR(50) NOT NULL,
                to_player VARCHAR(50) NOT NULL,
                from_airport INTEGER REFERENCES airports(id),
                to_airport INTEGER REFERENCES airports(id),
                contract_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status VARCHAR(20) DEFAULT 'active',
                acceptance_status VARCHAR(20) DEFAULT 'pending'
            )
        """)

        await db.execute("""
            CREATE TABLE IF NOT EXISTS player_activities (
                id INTEGER PRIMARY KEY,
                player_id VARCHAR(50) NOT NULL,
                airport_id INTEGER REFERENCES airports(id),
                accepted_level CHAR(1) CHECK (accepted_level IN ('A','B','C','D','E','F','G')) NOT NULL,
                last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Create indexes
        await db.execute('CREATE INDEX IF NOT EXISTS idx_contracts_date ON contracts(contract_date)')
        await db.execute('CREATE INDEX IF NOT EXISTS idx_contracts_status ON contracts(status)')
        await db.execute('CREATE INDEX IF NOT EXISTS idx_contracts_acceptance ON contracts(acceptance_status)')
        await db.execute('CREATE INDEX IF NOT EXISTS idx_contracts_from_player ON contracts(from_player)')
        await db.execute('CREATE INDEX IF NOT EXISTS idx_contracts_to_player ON contracts(to_player)')
        await db.execute('CREATE INDEX IF NOT EXISTS idx_activities_player ON player_activities(player_id)')
        await db.execute('CREATE INDEX IF NOT EXISTS idx_activities_date ON player_activities(last_active)')

        # Seed data - Airports
        airports_data = [
            ('INN', 'Innsbruck', 'Europe'),
            ('BRI', 'Brisbane', 'Australia'),
            ('IAD', 'Washington Dulles', 'North America'),
            ('SAN', 'San Diego', 'North America'),
            ('LHR', 'London Heathrow', 'Europe'),
            ('PRG', 'Prague', 'Europe'),
            ('PHX', 'Phoenix', 'North America'),
            ('NRT', 'Tokyo Narita', 'Asia'),
            ('MCT', 'Muscat', 'Middle East'),
            ('KWI', 'Kuwait', 'Middle East'),
            ('NGO', 'Nagoya', 'Asia'),
            ('LGA', 'New York LaGuardia', 'North America'),
            ('SXM', 'Sint Maarten', 'Caribbean'),
            ('BKK', 'Bangkok', 'Asia'),
        ]

        for code, name, region in airports_data:
            await db.execute(
                'INSERT OR IGNORE INTO airports (code, name, region) VALUES (?, ?, ?)', (code, name, region)
            )

        # Seed data for plane models will be loaded from CSV file separately

        await db.commit()


async def execute_safe_query(query: str, params: tuple = ()) -> List[Dict[str, Any]]:
    """Execute a read-only query safely"""
    # Basic safety checks
    query_lower = query.lower().strip()

    # Only allow SELECT queries
    if not query_lower.startswith('select'):
        raise ValueError('Only SELECT queries are allowed')

    # Prevent dangerous operations
    dangerous_keywords = ['insert', 'update', 'delete', 'drop', 'create', 'alter', 'exec', 'execute']
    if any(keyword in query_lower for keyword in dangerous_keywords):
        raise ValueError('Query contains dangerous keywords')

    async with aiosqlite.connect(DATABASE_PATH) as db:
        db.row_factory = aiosqlite.Row
        cursor = await db.execute(query, params)
        rows = await cursor.fetchall()
        return [dict(row) for row in rows]


async def get_dashboard_stats() -> Dict[str, Any]:
    """Get dashboard statistics"""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        db.row_factory = aiosqlite.Row

        # Total contracts
        cursor = await db.execute('SELECT COUNT(*) as count FROM contracts')
        total_contracts = (await cursor.fetchone())['count']

        # Active contracts
        cursor = await db.execute("SELECT COUNT(*) as count FROM contracts WHERE status = 'active'")
        active_contracts = (await cursor.fetchone())['count']

        # Total players
        cursor = await db.execute('SELECT COUNT(DISTINCT player_id) as count FROM player_activities')
        total_players = (await cursor.fetchone())['count']

        # Active players today
        cursor = await db.execute("""
            SELECT COUNT(DISTINCT player_id) as count
            FROM player_activities
            WHERE date(last_active) = date('now')
        """)
        active_players_today = (await cursor.fetchone())['count']

        # Top airports by activity
        cursor = await db.execute("""
            SELECT a.code, a.name, COUNT(*) as activity_count
            FROM player_activities pa
            JOIN airports a ON pa.airport_id = a.id
            GROUP BY a.id, a.code, a.name
            ORDER BY activity_count DESC
            LIMIT 5
        """)
        top_airports = [dict(row) for row in await cursor.fetchall()]

        return {
            'total_contracts': total_contracts,
            'active_contracts': active_contracts,
            'total_players': total_players,
            'active_players_today': active_players_today,
            'top_airports': top_airports,
        }
