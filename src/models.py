from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field


class PlaneLevel(str, Enum):
    A = 'A'
    B = 'B'
    C = 'C'
    D = 'D'
    E = 'E'
    F = 'F'
    G = 'G'


class PlaneType(str, Enum):
    PAX = 'PAX'
    CARGO = 'CARGO'


class ContractStatus(str, Enum):
    ACTIVE = 'active'
    COMPLETED = 'completed'
    CANCELLED = 'cancelled'


class ContractAcceptanceStatus(str, Enum):
    PENDING = 'pending'
    ACCEPTED = 'accepted'
    REJECTED = 'rejected'


# Database Models (Pydantic)
class AirportBase(BaseModel):
    code: str = Field(..., max_length=10)
    name: str = Field(..., max_length=100)
    region: Optional[str] = Field(None, max_length=50)


class Airport(AirportBase):
    id: int


class PlaneModelBase(BaseModel):
    model_config = {'protected_namespaces': ()}

    model_name: str = Field(..., max_length=50)
    level: PlaneLevel
    type: PlaneType


class PlaneModel(PlaneModelBase):
    id: int


class ContractBase(BaseModel):
    plane_model_id: int
    from_player: str = Field(..., max_length=50)
    to_player: str = Field(..., max_length=50)
    from_airport: int
    to_airport: int
    status: ContractStatus = ContractStatus.ACTIVE
    acceptance_status: ContractAcceptanceStatus = ContractAcceptanceStatus.PENDING


class ContractCreate(ContractBase):
    pass


class Contract(ContractBase):
    id: int
    contract_date: datetime

    class Config:
        from_attributes = True


class PlayerActivityBase(BaseModel):
    player_id: str = Field(..., max_length=50)
    airport_id: int
    accepted_level: PlaneLevel


class PlayerActivityCreate(PlayerActivityBase):
    pass


class PlayerActivity(PlayerActivityBase):
    id: int
    last_active: datetime

    class Config:
        from_attributes = True


# Request/Response Models
class NaturalQueryRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=500)


class NaturalQueryResponse(BaseModel):
    sql: str
    results: List[dict]
    interpretation: str


class ContractWithDetails(BaseModel):
    id: int
    contract_date: datetime
    status: ContractStatus
    acceptance_status: ContractAcceptanceStatus
    from_player: str
    to_player: str
    plane_model: PlaneModel
    from_airport: Airport
    to_airport: Airport


class ActivityWithDetails(BaseModel):
    id: int
    last_active: datetime
    player_id: str
    accepted_level: PlaneLevel
    airport: Airport


class DashboardStats(BaseModel):
    total_contracts: int
    active_contracts: int
    total_players: int
    active_players_today: int
    top_airports: List[dict]
    recent_activities: List[ActivityWithDetails]
